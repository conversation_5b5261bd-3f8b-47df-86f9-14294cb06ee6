package services

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"testing"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v4/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

func TestSendSMS(t *testing.T) {
	// 电信
	//phoneNumber := "18938888061"
	// 移动
	phoneNumber := "13418852584"
	//phoneNumber := "15914138067"
	//phoneNumber := "14774972450"
	//phoneNumber := "13725519305"
	// 联通
	//phoneNumber := "15623076813"
	v := rand.Intn(1000000)
	code := fmt.Sprintf("%06d", v)

	templateParam := map[string]string{
		"code": code,
	}

	paramJSON, err := json.Marshal(templateParam)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("json marshal err: %v", err)
	}

	request := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers: tea.String(phoneNumber),
		//SignName:     tea.String("卓联软件"),
		SignName:      tea.String("音活"),
		TemplateCode:  tea.String("SMS_315720114"),
		TemplateParam: tea.String(string(paramJSON)),
	}

	aliConfig := &openapi.Config{
		AccessKeyId:     tea.String("LTAI5tGQgk1kzpYb7EndyuFR"),
		AccessKeySecret: tea.String("******************************"),
		Endpoint:        tea.String("dysmsapi.aliyuncs.com"),
	}

	client, err := dysmsapi20170525.NewClient(aliConfig)
	if err != nil {
		t.Errorf("NewClient error: %v", err)
	}

	response, err := client.SendSmsWithOptions(request, &util.RuntimeOptions{})
	if err != nil {
		t.Errorf("SendSmsWithOptions err: %v", err)
	}

	if *response.Body.Code != "OK" {
		t.Errorf("短信发送响应错误: %s, %s", *response.Body.Code, *response.Body.Message)
	}

	t.Logf("SendSmsWithOptions: %v", *response.Body)
}
