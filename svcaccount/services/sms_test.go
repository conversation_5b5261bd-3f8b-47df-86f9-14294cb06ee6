package services

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"sync"
	"testing"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v4/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

func TestSendSMS(t *testing.T) {
	// 电信
	//phoneNumber := "18938888061"
	// 移动
	phoneNumber := "13418852584"
	//phoneNumber := "15914138067"
	//phoneNumber := "14774972450"
	//phoneNumber := "13725519305"
	// 联通
	//phoneNumber := "15623076813"
	v := rand.Intn(1000000)
	code := fmt.Sprintf("%06d", v)

	templateParam := map[string]string{
		"code": code,
	}

	paramJ<PERSON><PERSON>, err := json.Marshal(templateParam)
	if err != nil {
		t.Errorf("json marshal err: %v", err)
	}

	request := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers: tea.String(phoneNumber),
		//SignName:     tea.String("卓联软件"),
		SignName:      tea.String("音活"),
		TemplateCode:  tea.String("SMS_315720114"),
		TemplateParam: tea.String(string(paramJSON)),
	}

	aliConfig := &openapi.Config{
		AccessKeyId:     tea.String("LTAI5tGQgk1kzpYb7EndyuFR"),
		AccessKeySecret: tea.String("******************************"),
		Endpoint:        tea.String("dysmsapi.aliyuncs.com"),
	}

	client, err := dysmsapi20170525.NewClient(aliConfig)
	if err != nil {
		t.Errorf("NewClient error: %v", err)
	}

	response, err := client.SendSmsWithOptions(request, &util.RuntimeOptions{})
	if err != nil {
		t.Errorf("SendSmsWithOptions err: %v", err)
	}

	if *response.Body.Code != "OK" {
		t.Errorf("短信发送响应错误: %s, %s", *response.Body.Code, *response.Body.Message)
	}

	t.Logf("SendSmsWithOptions: %v", *response.Body)
}

// TestSendSMSPeriodic 周期性发送短信测试方法
// 每5分钟向预定义的移动手机号码列表发送短信
func TestSendSMSPeriodic(t *testing.T) {
	// 预定义的移动手机号码列表
	phoneNumbers := []string{
		"13418852584", // 移动
		"15914138067", // 移动
		"14774972450", // 移动
		"13725519305", // 移动
	}

	// 创建context用于优雅停止
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建等待组用于同步
	var wg sync.WaitGroup

	// 创建阿里云短信客户端
	aliConfig := &openapi.Config{
		AccessKeyId:     tea.String("LTAI5tGQgk1kzpYb7EndyuFR"),
		AccessKeySecret: tea.String("******************************"),
		Endpoint:        tea.String("dysmsapi.aliyuncs.com"),
	}

	client, err := dysmsapi20170525.NewClient(aliConfig)
	if err != nil {
		t.Fatalf("创建短信客户端失败: %v", err)
	}

	// 短信发送函数
	sendSMS := func(phoneNumber string) error {
		// 生成随机验证码
		v := rand.Intn(1000000)
		code := fmt.Sprintf("%06d", v)

		templateParam := map[string]string{
			"code": code,
		}

		paramJSON, err := json.Marshal(templateParam)
		if err != nil {
			return fmt.Errorf("json marshal err: %v", err)
		}

		request := &dysmsapi20170525.SendSmsRequest{
			PhoneNumbers:  tea.String(phoneNumber),
			SignName:      tea.String("音活"),
			TemplateCode:  tea.String("SMS_315720114"),
			TemplateParam: tea.String(string(paramJSON)),
		}

		response, err := client.SendSmsWithOptions(request, &util.RuntimeOptions{})
		if err != nil {
			return fmt.Errorf("发送短信失败: %v", err)
		}

		if *response.Body.Code != "OK" {
			return fmt.Errorf("短信发送响应错误: %s, %s", *response.Body.Code, *response.Body.Message)
		}

		t.Logf("成功发送短信到 %s, 验证码: %s, 响应: %v", phoneNumber, code, *response.Body)
		return nil
	}

	// 批量发送短信函数
	sendBatchSMS := func() {
		t.Logf("开始批量发送短信，时间: %s", time.Now().Format("2006-01-02 15:04:05"))

		for _, phoneNumber := range phoneNumbers {
			if err := sendSMS(phoneNumber); err != nil {
				t.Errorf("发送短信到 %s 失败: %v", phoneNumber, err)
			} else {
				t.Logf("成功发送短信到 %s", phoneNumber)
			}

			// 每个号码之间间隔1秒，避免频率过高
			time.Sleep(1 * time.Second)
		}

		t.Logf("批量发送短信完成，时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	}

	// 启动周期性发送goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()

		// 创建5分钟定时器
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()

		t.Logf("周期性短信发送服务启动，每5分钟执行一次")

		// 立即执行一次
		sendBatchSMS()

		for {
			select {
			case <-ctx.Done():
				t.Logf("收到停止信号，周期性短信发送服务正在停止...")
				return
			case <-ticker.C:
				sendBatchSMS()
			}
		}
	}()

	// 运行测试一段时间（这里设置为15分钟，可以根据需要调整）
	// 在实际使用中，可以通过外部信号或其他方式控制停止时间
	testDuration := 15 * time.Minute
	t.Logf("测试将运行 %v，然后自动停止", testDuration)

	select {
	case <-time.After(testDuration):
		t.Logf("测试时间到达，正在停止周期性发送...")
		cancel()
	case <-ctx.Done():
		t.Logf("测试被提前停止")
	}

	// 等待goroutine完成
	wg.Wait()
	t.Logf("周期性短信发送测试完成")
}
